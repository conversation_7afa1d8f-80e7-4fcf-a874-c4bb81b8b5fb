import type { FC } from 'react';
import { useMemo, useState, useCallback, useEffect } from 'react';
import { Modal, Form, Input, Space, Typography, Divider, message } from 'antd';
import { addOrUpdateStoreIcon } from '@/services/api/store';
import type { RcFile } from 'antd/lib/upload';
import { scenicHost } from '@/services/api';
import { getEnv } from '@/common/utils/getEnv';
import ProSvg from '@/common/components/ProSvg';

// 添加样式
const modalStyles = `
  .image-upload-container:hover .hover-mask {
    opacity: 1 !important;
  }
`;

// 创建样式标签
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = modalStyles;
  if (!document.head.querySelector('style[data-component="CreateIconModal"]')) {
    styleElement.setAttribute('data-component', 'CreateIconModal');
    document.head.appendChild(styleElement);
  }
}

type IconInfo = {
  id?: string | number;
  name: string;
  icon: string; // 未选中
  icon_active: string; // 选中
};

// 图片上传逻辑hook - 与RudderNavForm.tsx保持一致
const useImageUpload = (onImageUpload: (type: 'active' | 'inactive', imageUrl: string) => void) => {
  const { FILE_HOST } = getEnv();
  const [uploading, setUploading] = useState<'active' | 'inactive' | null>(null);

  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只允许上传 jpeg,png 格式的图片！');
    }
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('图片必须小于10MB!');
    }
    return isJpgOrPng && isLt10M;
  };

  const handleUpload = (type: 'active' | 'inactive') => {
    if (uploading !== null) return; // 防止重复上传

    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/png, image/jpeg';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file && beforeUpload(file as RcFile)) {
        setUploading(type);
        const formData = new FormData();
        formData.append('file', file);

        // 使用fetch模拟Upload组件的上传逻辑
        fetch(scenicHost + '/aws/uploadFile', {
          method: 'POST',
          body: formData,
        })
          .then((response) => response.json())
          .then((response) => {
            if (response.data?.path) {
              onImageUpload(type, FILE_HOST + response.data.path);
              message.success('图片上传成功');
            } else {
              message.error('上传失败，请重试');
            }
          })
          .catch(() => {
            message.error('上传失败，请重试');
          })
          .finally(() => {
            setUploading(null);
          });
      }
    };
    input.click();
  };

  return { handleUpload, uploading };
};

type CreateIconModalProps = {
  open: boolean;
  onCancel: () => void;
  onOk?: (icon: IconInfo) => void;
  storeId?: string | number;
  editing?: IconInfo;
};

const CreateIconModal: FC<CreateIconModalProps> = ({ open, onCancel, onOk, storeId, editing }) => {
  const [form] = Form.useForm();
  const [activeImageUrl, setActiveImageUrl] = useState<string>('');
  const [inactiveImageUrl, setInactiveImageUrl] = useState<string>('');

  // 图片上传处理函数
  const handleImageUpload = (type: 'active' | 'inactive', imageUrl: string) => {
    if (type === 'active') {
      setActiveImageUrl(imageUrl);
    } else {
      setInactiveImageUrl(imageUrl);
    }
  };

  // 使用图片上传hook
  const { handleUpload, uploading } = useImageUpload(handleImageUpload);

  // 图标渲染函数 - 与RudderNavForm.tsx保持一致
  const renderIcon = (src?: string) => {
    if (!src)
      return (
        <div
          style={{
            width: '64px',
            height: '64px',
            backgroundColor: '#f3f3f3',
            border: '1px dashed #ddd',
            borderRadius: '6px',
          }}
        />
      );
    return typeof src === 'string' && src.includes('http') ? (
      <img
        src={src}
        style={{
          maxWidth: '100%',
          maxHeight: '100%',
          width: 'auto',
          height: 'auto',
          objectFit: 'contain',
        }}
      />
    ) : (
      <ProSvg src={src} width="100%" height="100%" />
    );
  };

  // 当编辑的图标变化时，重置状态和表单
  useEffect(() => {
    if (open) {
      // 重置图片上传状态
      setActiveImageUrl(editing?.icon_active || '');
      setInactiveImageUrl(editing?.icon || '');

      // 重置并设置表单初始值
      form.resetFields();
      if (editing?.name) {
        form.setFieldsValue({ name: editing.name });
      }
    }
  }, [open, editing, form]);

  const handleOk = async () => {
    try {
      // 获取表单值
      const values = form.getFieldsValue();
      const hasActive = activeImageUrl || editing?.icon_active;
      const hasInactive = inactiveImageUrl || editing?.icon;

      // 验证图标名称
      if (!values?.name?.trim()) {
        message.warning('请填写图标名称');
        return;
      }

      // 验证选中状态图标
      if (!hasActive) {
        message.warning('请上传选中状态图标文件');
        return;
      }

      // 验证未选中状态图标
      if (!hasInactive) {
        message.warning('请上传未选中状态图标文件');
        return;
      }

      // 验证表单字段
      await form.validateFields();

      const payload: IconInfo = {
        id: editing?.id,
        name: values.name,
        icon_active: activeImageUrl || editing?.icon_active || '',
        icon: inactiveImageUrl || editing?.icon || '',
      };

      await addOrUpdateStoreIcon({
        id: payload.id,
        storeId: storeId as any,
        name: payload.name,
        selectedIcon: payload.icon_active,
        unselectedIcon: payload.icon,
      });

      message.success(editing?.id ? '编辑成功' : '新增成功');
      onOk?.({ ...payload, id: payload.id });
    } catch (err) {
      // 如果是表单验证错误，不需要额外处理，antd会自动显示错误信息
    }
  };

  // 编辑态初始化见上方 initialActive/initialInactive

  return (
    <Modal
      width={720}
      title="新建图标"
      open={open}
      onCancel={onCancel}
      onOk={handleOk}
      okText="保存"
      cancelText="取消"
      destroyOnClose
    >
      <Form form={form} layout="vertical" preserve={false}>
        <Form.Item
          label="图标名称："
          name="name"
          rules={[{ required: true, message: '请输入图标名称' }]}
        >
          <Input placeholder="请输入" maxLength={8} showCount />
        </Form.Item>

        <div style={{ marginBottom: 8, fontWeight: 500 }}>上传图标：</div>
        <Typography.Text type="secondary">支持jpg/jpeg/png格式，建议尺寸100*100px</Typography.Text>

        <Divider style={{ margin: '12px 0 16px' }} />

        <Space size={40} align="start">
          {/* 选中状态图标上传 */}
          <div style={{ textAlign: 'center' }}>
            <div>
              {!(activeImageUrl || editing?.icon_active) ? (
                <div
                  style={{ display: 'inline-flex', flexDirection: 'column', alignItems: 'center' }}
                >
                  <div
                    style={{
                      width: '176px',
                      height: '97px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      flexDirection: 'column',
                      background: '#fafafa',
                      border: '1px dashed #d9d9d9',
                      borderRadius: '8px',
                      cursor: uploading === 'active' ? 'not-allowed' : 'pointer',
                      opacity: uploading === 'active' ? 0.6 : 1,
                    }}
                    onClick={() => uploading !== 'active' && handleUpload('active')}
                  >
                    <div style={{ marginTop: '8px', color: '#bfbfbf', fontSize: '12px' }}>
                      {uploading === 'active' ? '上传中...' : '添加图标'}
                    </div>
                  </div>
                </div>
              ) : (
                <div
                  style={{ display: 'inline-flex', flexDirection: 'column', alignItems: 'stretch' }}
                >
                  <div
                    className="image-upload-container"
                    style={{
                      position: 'relative',
                      width: '200px',
                      padding: '12px 0',
                      background: '#f7f7f7',
                      borderRadius: '8px',
                      cursor: uploading === 'active' ? 'not-allowed' : 'pointer',
                      opacity: uploading === 'active' ? 0.6 : 1,
                    }}
                    onClick={() => uploading !== 'active' && handleUpload('active')}
                  >
                    <div
                      style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                    >
                      <div style={{ width: '50%', display: 'flex', justifyContent: 'center' }}>
                        <div
                          style={{
                            width: '64px',
                            height: '64px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          {renderIcon(activeImageUrl || editing?.icon_active)}
                        </div>
                      </div>
                    </div>
                    <div
                      style={{
                        position: 'absolute',
                        left: 0,
                        right: 0,
                        bottom: 0,
                        height: '32px',
                        background: 'rgba(0, 0, 0, 0.45)',
                        color: '#fff',
                        textAlign: 'center',
                        lineHeight: '32px',
                        borderRadius: '0 0 8px 8px',
                        opacity: 0,
                        transition: 'opacity 0.2s ease',
                        cursor: 'pointer',
                      }}
                      className="hover-mask"
                    >
                      {uploading === 'active' ? '上传中...' : '更换图片'}
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div style={{ marginTop: 8, color: '#999' }}>选中状态</div>
          </div>

          {/* 未选中状态图标上传 */}
          <div style={{ textAlign: 'center' }}>
            <div>
              {!(inactiveImageUrl || editing?.icon) ? (
                <div
                  style={{ display: 'inline-flex', flexDirection: 'column', alignItems: 'center' }}
                >
                  <div
                    style={{
                      width: '176px',
                      height: '97px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      flexDirection: 'column',
                      background: '#fafafa',
                      border: '1px dashed #d9d9d9',
                      borderRadius: '8px',
                      cursor: uploading === 'inactive' ? 'not-allowed' : 'pointer',
                      opacity: uploading === 'inactive' ? 0.6 : 1,
                    }}
                    onClick={() => uploading !== 'inactive' && handleUpload('inactive')}
                  >
                    <div style={{ marginTop: '8px', color: '#bfbfbf', fontSize: '12px' }}>
                      {uploading === 'inactive' ? '上传中...' : '添加图标'}
                    </div>
                  </div>
                </div>
              ) : (
                <div
                  style={{ display: 'inline-flex', flexDirection: 'column', alignItems: 'stretch' }}
                >
                  <div
                    className="image-upload-container"
                    style={{
                      position: 'relative',
                      width: '200px',
                      padding: '12px 0',
                      background: '#f7f7f7',
                      borderRadius: '8px',
                      cursor: uploading === 'inactive' ? 'not-allowed' : 'pointer',
                      opacity: uploading === 'inactive' ? 0.6 : 1,
                    }}
                    onClick={() => uploading !== 'inactive' && handleUpload('inactive')}
                  >
                    <div
                      style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                    >
                      <div style={{ width: '50%', display: 'flex', justifyContent: 'center' }}>
                        <div
                          style={{
                            width: '64px',
                            height: '64px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          {renderIcon(inactiveImageUrl || editing?.icon)}
                        </div>
                      </div>
                    </div>
                    <div
                      style={{
                        position: 'absolute',
                        left: 0,
                        right: 0,
                        bottom: 0,
                        height: '32px',
                        background: 'rgba(0, 0, 0, 0.45)',
                        color: '#fff',
                        textAlign: 'center',
                        lineHeight: '32px',
                        borderRadius: '0 0 8px 8px',
                        opacity: 0,
                        transition: 'opacity 0.2s ease',
                        cursor: 'pointer',
                      }}
                      className="hover-mask"
                    >
                      {uploading === 'inactive' ? '上传中...' : '更换图片'}
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div style={{ marginTop: 8, color: '#999' }}>未选中状态</div>
          </div>
        </Space>
      </Form>
    </Modal>
  );
};

export default CreateIconModal;


